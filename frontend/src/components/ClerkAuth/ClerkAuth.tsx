import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { 
  SignedIn, 
  SignedOut, 
  SignInButton, 
  SignUpButton, 
  UserButton,
  useAuth
} from '@clerk/clerk-react';
import * as styles from './ClerkAuth.css';

/**
 * Component that requires authentication to access
 * Redirects to login page if user is not authenticated
 */
export const RequireAuth: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoaded, isSignedIn } = useAuth();

  if (!isLoaded) {
    return <div>Loading authentication...</div>;
  }

  if (!isSignedIn) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

/**
 * Layout for protected routes
 * Includes the main application layout with sidebar and navbar
 */
export const ProtectedLayout = () => {
  return (
    <RequireAuth>
      <Outlet />
    </RequireAuth>
  );
};

/**
 * Authentication buttons for the navbar
 */
export const AuthButtons = () => {
  return (
    <div className={styles.authButtonsContainer}>
      <SignedOut>
        <SignInButton mode="modal" />
        <SignUpButton mode="modal" />
      </SignedOut>
      <SignedIn>
        <UserButton afterSignOutUrl="/login" />
      </SignedIn>
    </div>
  );
};

export default {
  RequireAuth,
  ProtectedLayout,
  AuthButtons
};
