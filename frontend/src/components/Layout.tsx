import { Outlet } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { useState, useEffect } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import * as styles from './layoutStyles.css.ts';

const Layout = () => {
  const { user } = useUser();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Function to handle sidebar toggle
  const handleSidebarToggle = (isOpen: boolean) => {
    setSidebarOpen(isOpen);
  };

  return (
    <div className={styles.layoutContainer}>
      <Navbar />
      <Sidebar onToggle={handleSidebarToggle} />
      <main className={`${styles.contentContainer} ${sidebarOpen ? '' : styles.contentContainerExpanded}`} style={{ display: 'block', visibility: 'visible', opacity: 1, position: 'relative', zIndex: 1 }}>
        <div style={{ display: 'block', visibility: 'visible', opacity: 1, position: 'relative', zIndex: 1 }}>
          <Outlet />
        </div>
      </main>
    </div>
  );
};

export default Layout;