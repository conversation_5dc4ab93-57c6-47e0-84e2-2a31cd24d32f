// Explicitly import React to ensure it's available
import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { ClerkProvider } from '@clerk/clerk-react';
import App from './App';
import './styles/globalStyles.css.ts';

// Get the Clerk publishable key from environment variables
const CLERK_PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

// Check if the Clerk publishable key is available
if (!CLERK_PUBLISHABLE_KEY) {
  throw new Error('Missing Clerk Publishable Key');
}

// Configure future flags for React Router v7
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

// Get the root element and create the root
const rootElement = document.getElementById('root');

if (!rootElement) {
  console.error('Root element not found!');
} else {
  const root = ReactDOM.createRoot(rootElement);

  // Wrap rendering in try/catch to catch any errors
  try {
    root.render(
      <React.StrictMode>
        <ClerkProvider
          publishableKey={CLERK_PUBLISHABLE_KEY}
          afterSignOutUrl="/login"
          appearance={{
            layout: {
              socialButtonsVariant: "iconButton",
              showOptionalFields: false,
              logoPlacement: "inside",
            },
            elements: {
              rootBox: {
                width: '100%',
                margin: '0 auto',
              },
              card: {
                boxShadow: 'none',
                width: '100%',
                maxWidth: '400px',
              },
              formButtonPrimary: {
                backgroundColor: '#0070f3',
                fontSize: '16px',
                padding: '10px 20px',
              },
              formFieldInput: {
                padding: '10px',
                fontSize: '16px',
              },
            }
          }}
        >
          <BrowserRouter {...router}>
            <App />
          </BrowserRouter>
        </ClerkProvider>
      </React.StrictMode>
    );
  } catch (error) {
    console.error('Error rendering React application:', error);

    // Show a fallback UI if rendering fails
    rootElement.innerHTML = `
      <div style="padding: 20px; margin: 20px; background-color: #ffebee; border: 1px solid #ffcdd2; border-radius: 5px;">
        <h2>Error Rendering Application</h2>
        <p>An error occurred while rendering the application:</p>
        <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">${error}</pre>
      </div>
    `;
  }
}