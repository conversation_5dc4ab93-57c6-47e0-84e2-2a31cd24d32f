import { style, globalStyle } from '@vanilla-extract/css';

export const container = style({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '100vh',
  padding: '20px',
  backgroundColor: '#f5f5f5'
});

export const card = style({
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '2rem',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  width: '100%',
  maxWidth: '400px'
});

export const form = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '1rem'
});

export const formGroup = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem'
});

export const input = style({
  padding: '0.75rem',
  borderRadius: '4px',
  border: '1px solid #ddd',
  fontSize: '1rem',
  selectors: {
    '&:focus': {
      outline: 'none',
      borderColor: '#0070f3'
    }
  }
});

export const submitButton = style({
  padding: '0.75rem',
  backgroundColor: '#0070f3',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  cursor: 'pointer',
  selectors: {
    '&:disabled': {
      backgroundColor: '#ccc',
      cursor: 'not-allowed'
    },
    '&:hover:not(:disabled)': {
      backgroundColor: '#0060df'
    }
  }
});

export const error = style({
  color: '#dc2626',
  fontSize: '0.875rem',
  marginTop: '0.5rem'
});

export const links = style({
  marginTop: '1rem',
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
  textAlign: 'center',
  fontSize: '0.875rem'
});

// Global styles for links within the links container
globalStyle(`${links} a`, {
  color: '#0070f3',
  textDecoration: 'none'
});

globalStyle(`${links} a:hover`, {
  textDecoration: 'underline'
});

export const clerkContainer = style({
  width: '100%',
  marginTop: '1rem',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
});