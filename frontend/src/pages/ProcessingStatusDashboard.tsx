import { useEffect, useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useRolewiseStore } from '../stores/rolewiseStore';
import LoadingSpinner from '../components/LoadingSpinner';
import { formatDistanceToNow, format } from 'date-fns';
import * as styles from './processingStatusDashboardStyles.css.ts';

interface JobMetrics {
  applicationCount: number;
  groupCount: number;
  userCount: number;
  userGroupMembershipCount: number;
  batchesProcessed: number;
  processingTimeSeconds: number;
  inferenceTriggered: boolean;
  inferenceSuccess: boolean;
}

const ProcessingStatusDashboard = () => {
  const { user } = useAuthStore();
  const {
    processingJobs,
    processingJobsLoading,
    fetchProcessingJobs,
    selectedJobId,
    selectJob,
    error,
    acknowledgeError,
  } = useRolewiseStore();

  const [refreshing, setRefreshing] = useState(false);

  // Get the tenant ID from the user
  const tenantId = user?.user_metadata?.tenant_id;

  // Fetch processing jobs when the component mounts
  useEffect(() => {
    if (tenantId) {
      fetchProcessingJobs(tenantId);
    }
  }, [tenantId, fetchProcessingJobs]);

  // Handle refresh button click
  const handleRefresh = async () => {
    if (tenantId) {
      setRefreshing(true);
      await fetchProcessingJobs(tenantId);
      setRefreshing(false);
    }
  };

  // Get the selected job
  const selectedJob = selectedJobId
    ? processingJobs.find(job => job.id === selectedJobId)
    : processingJobs[0];

  // Function to get the status badge style based on job status
  const getStatusBadgeStyle = (status: string | null) => {
    switch (status) {
      case 'pending':
        return styles.statusPendingStyle;
      case 'fetching':
        return styles.statusFetchingStyle;
      case 'processing':
        return styles.statusProcessingStyle;
      case 'inferring':
        return styles.statusInferringStyle;
      case 'completed':
        return styles.statusCompletedStyle;
      case 'failed':
        return styles.statusFailedStyle;
      default:
        return styles.statusPendingStyle;
    }
  };

  // Function to get a human-readable status message
  const getStatusMessage = (status: string | null) => {
    switch (status) {
      case 'pending':
        return 'Preparing to process data...';
      case 'fetching':
        return 'Fetching data from Microsoft Entra ID...';
      case 'processing':
        return 'Processing tenant data...';
      case 'inferring':
        return 'Inferring roles from user data...';
      case 'completed':
        return 'Processing completed successfully';
      case 'failed':
        return 'Processing failed';
      default:
        return 'Unknown status';
    }
  };

  // Function to calculate progress percentage based on job status
  const getProgressPercentage = (job: any) => {
    if (!job) return 0;
    
    switch (job.status) {
      case 'pending':
        return 10;
      case 'fetching':
        return 30;
      case 'processing':
        return 60;
      case 'inferring':
        return 80;
      case 'completed':
        return 100;
      case 'failed':
        return 100;
      default:
        return 0;
    }
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Function to format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  // Function to extract metrics from job metadata
  const extractMetrics = (job: any): JobMetrics => {
    if (!job || !job.metadata) {
      return {
        applicationCount: 0,
        groupCount: 0,
        userCount: 0,
        userGroupMembershipCount: 0,
        batchesProcessed: 0,
        processingTimeSeconds: 0,
        inferenceTriggered: false,
        inferenceSuccess: false,
      };
    }

    return {
      applicationCount: job.metadata.application_count || 0,
      groupCount: job.metadata.group_count || 0,
      userCount: job.metadata.user_count || 0,
      userGroupMembershipCount: job.metadata.user_group_membership_count || 0,
      batchesProcessed: job.metadata.batches_processed || 0,
      processingTimeSeconds: job.metadata.processing_time_seconds || 0,
      inferenceTriggered: job.metadata.inference_triggered || false,
      inferenceSuccess: job.metadata.inference_success || false,
    };
  };

  // Calculate processing time in a human-readable format
  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }
  };

  if (processingJobsLoading && processingJobs.length === 0) {
    return (
      <div className={styles.dashboardContainer}>
        <div className={styles.headerStyle}>
          <h1 className={styles.titleStyle}>Processing Status Dashboard</h1>
        </div>
        <div className={styles.loadingStyle}>
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.dashboardContainer}>
        <div className={styles.headerStyle}>
          <h1 className={styles.titleStyle}>Processing Status Dashboard</h1>
        </div>
        <div className={styles.errorMessageStyle}>
          <p>Error: {error}</p>
          <button onClick={acknowledgeError}>Dismiss</button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.dashboardContainer}>
      <div className={styles.headerStyle}>
        <h1 className={styles.titleStyle}>Processing Status Dashboard</h1>
        <button 
          className={styles.refreshButtonStyle} 
          onClick={handleRefresh}
          disabled={refreshing || processingJobsLoading}
        >
          {refreshing && <span className={styles.spinnerStyle}></span>}
          Refresh
        </button>
      </div>

      {processingJobs.length === 0 ? (
        <div className={styles.cardStyle}>
          <p>No processing jobs found. Start a data processing job from the Dashboard.</p>
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {/* Current Job Status Card */}
          {selectedJob && (
            <div className={styles.cardStyle}>
              <h2 className={styles.sectionTitleStyle}>Job Status</h2>
              <div>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <span className={getStatusBadgeStyle(selectedJob.status)}>
                    {selectedJob.status}
                  </span>
                  <span style={{ marginLeft: '0.5rem' }}>{getStatusMessage(selectedJob.status)}</span>
                </div>

                {/* Progress Bar */}
                <div className={styles.progressBarContainerStyle}>
                  <div 
                    className={styles.progressBarStyle} 
                    style={{ width: `${getProgressPercentage(selectedJob)}%` }}
                  ></div>
                </div>

                {/* Job Details */}
                <div className={styles.jobDetailsStyle}>
                  <p>Job ID: {selectedJob.id}</p>
                  <p>Started: {formatDate(selectedJob.started_at)} ({formatRelativeTime(selectedJob.started_at)})</p>
                  {selectedJob.completed_at && (
                    <p>Completed: {formatDate(selectedJob.completed_at)} ({formatRelativeTime(selectedJob.completed_at)})</p>
                  )}
                  {selectedJob.error && (
                    <p style={{ color: '#ef4444' }}>Error: {selectedJob.error}</p>
                  )}
                </div>

                {/* Job Metrics */}
                {(selectedJob.status === 'completed' || selectedJob.status === 'failed') && (
                  <div>
                    <h3 className={styles.sectionTitleStyle}>Processing Metrics</h3>
                    <div className={styles.metadataStyle}>
                      {Object.entries(extractMetrics(selectedJob)).map(([key, value]) => {
                        // Skip boolean values that are false
                        if (typeof value === 'boolean' && !value) return null;
                        
                        // Format the key for display
                        const formattedKey = key
                          .replace(/([A-Z])/g, ' $1')
                          .replace(/^./, str => str.toUpperCase());
                        
                        // Format the value based on its type
                        let formattedValue = value;
                        if (key === 'processingTimeSeconds') {
                          formattedValue = formatProcessingTime(value as number);
                        } else if (typeof value === 'boolean') {
                          formattedValue = value ? 'Yes' : 'No';
                        }
                        
                        return (
                          <div key={key} className={styles.metadataItemStyle}>
                            <span className={styles.metadataLabelStyle}>{formattedKey}</span>
                            <span className={styles.metadataValueStyle}>{formattedValue}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Job History Card */}
          <div className={styles.cardStyle}>
            <h2 className={styles.sectionTitleStyle}>Job History</h2>
            <div className={styles.jobListStyle}>
              {processingJobs.map(job => (
                <div 
                  key={job.id} 
                  className={job.id === selectedJob?.id ? styles.selectedJobItemStyle : styles.jobItemStyle}
                  onClick={() => selectJob(job.id)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span className={getStatusBadgeStyle(job.status)}>{job.status}</span>
                    <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                      {formatRelativeTime(job.started_at)}
                    </span>
                  </div>
                  <div>
                    {job.status === 'completed' && (
                      <span>Processed {job.metadata?.application_count || 0} applications, {job.metadata?.group_count || 0} groups, {job.metadata?.user_count || 0} users</span>
                    )}
                    {job.status === 'failed' && (
                      <span style={{ color: '#ef4444' }}>Error: {job.error || 'Unknown error'}</span>
                    )}
                    {['pending', 'fetching', 'processing', 'inferring'].includes(job.status) && (
                      <span>{getStatusMessage(job.status)}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessingStatusDashboard;
